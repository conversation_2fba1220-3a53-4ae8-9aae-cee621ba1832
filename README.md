# konva-marker

## Project setup
```
pnpm install
```

### Compiles and hot-reloads for development
```
pnpm run serve
```

### Compiles and minifies for production
```
pnpm run build
```

### Lints and fixes files
```
pnpm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).


### openseadragon版本变动
openseadragon版本从@2.4.2升级到@4.1.1

### 0.7.350 -> 0.7.351
解决只有PC端左键能够绘画的问题 增加了移动端的判断

### 0.7.353
增加AI标注的镜像处理

### 0.7.354
封装了截图功能，支持自定义工具栏

### 0.7.355
修改AI标注组件Coor结构改变的问题

### 0.7.356
去掉多边形首尾闭合的判断  修改多边形的交互逻辑问题  
去掉自由曲线的

### 1.0.0
重构了大部分标注代码 抽离为类 并且合并了面积计算分支的代码

### 1.0.1
解决合并之后存在的问题 

### 1.0.21
解决闭合曲线首尾相加会取消的问题

### 1.0.22
修改双击会产生label的问题 更新标尺

### 1.0.23
胃-AI-定位功能的开发

### 1.0.24
尺子功能的开发

### 1.0.25
标旗的标注功能开发完善、夹角的功能开发

### 1.0.251
修改定位问题 优化夹角和标旗

### 1.0.26
增加同心圆的标注方法

### 1.0.27
优化同心圆和尺子的逻辑代码

## 1.0.272
解决线上环境报错问题（imageToViewportCoordinates的问题）

## 1.0.273
解决编辑状态下点击已有矩形会存在的报错问题 

## 1.0.274
解决label单例导致数据对不上的问题

## 1.0.275
fix:
    1. 标旗工具-画完标注后没有保存
    2. 标注工具-选中同心圆后，会出现蓝色覆盖(修改圆点半径)
    3. 解决编辑时点击锚机产生的bug

## 1.0.276
    添加功能：标旗选中无法resize 

## 1.0.277
    新增标注和AI标注的显示隐藏

## 1.0.278
    标注隐藏时退出编辑模式

## 1.0.279
    新加截图返回对应的OSD图像坐标

## 1.0.280
    修改截图相关功能 增加几个通用函数

## 1.0.281
    优化截图交互

## 1.0.281
    解决截图类被创建多个时 自定义DOM丢失的问题

## 1.0.29
    增加切片库JSON转换为阅片软件的JSON方法，给标注增加像素坐标相关属性

## 1.0.291
    增加阅片软件的JSON转换为切片库JSON方法

## 1.0.292
    优化JSON互相转换的方法

## 1.0.293
    修改代码实现标注协议的统一

## 1.0.294
    协议统一的bug修改，增加肠的AIJSON处理

## 1.0.295
    修改夹角的角度问题

## 1.0.296
    解决scene console太多的问题

## 1.0.297
    解决转为标注协议时出现的无法渲染的问题

## 1.0.298
    修改ruler标注存在的问题

## 1.0.299
    修改转换协议时有些属性没有传递的问题(对旧数据应保留旧数据的结构)

## 1.0.300
    新加宫颈、前列腺AI预览

## 1.0.301
    测量尺的长度居中展示

## 1.0.302
    测量尺的选中更新

## 1.0.303
    解决面积处理的相关问题

## 1.0.304
    解决JSON转换ID重复的问题

## 1.0.305
    增加HK相关赛维森功能代码

## 1.0.306
    修改pixioverlay创建方法

## 1.0.307
    解决rect面积无法显示的问题

## 1.0.308
    增加分组解决方法

## 1.0.309
    解决分组新加之后出现的问题，解决JSON互相转换存在的问题

## 1.0.310
    解决分组返回json重复的问题

## 1.0.311
    解决分组递归处理出现的问题

## 1.0.312
    新增批量删除标注方法

## 1.0.313
    修改setattr方法，增加setLabelAttr方法

## 1.0.314
    标旗增加组

## 1.0.315
    增加修改分组名的方法

## 1.0.316
    增加截图时的标注、全屏截图、比例尺自动添加功能

## 1.0.317
    增加打包后的types文件

## 1.0.318
    完善isMouseLeftOrTouch的判断逻辑，解决移动端falg无法触发drawend的问题

## 1.0.319
    完善flag移动端双击的问题

