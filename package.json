{"name": "sqray-marker", "version": "1.0.319", "main": "lib/sqray-marker.umd.min.js", "module": "./src/index.js", "private": false, "types": "lib/types/components/index.d.ts", "files": ["lib"], "scripts": {"serve": "vue-cli-service serve --mode -development", "dev": "vue-cli-service serve --mode -development", "build": "npm run build:lib && npm run build:types", "build:lib": "vue-cli-service build --target lib --dest lib ./src/index.ts", "build:types": "tsc --declaration --emitDeclarationOnly --project tsconfig.lib.json", "lint": "vue-cli-service lint", "api": "rimraf ./src/services && pont list"}, "publishConfig": {"registry": "http://*************:8081/repository/npm-hosted/"}, "dependencies": {"@types/openseadragon": "^3.0.10", "browser-fs-access": "^0.31.0", "core-js": "^3.8.3", "dayjs": "^1.11.13", "geometric": "2.5.5", "konva": "9.3.22", "lodash.merge": "^4.6.2", "mitt": "^3.0.1", "nertc-web-sdk": "^5.5.30", "normalize.css": "^8.0.1", "openseadragon": "^4.1.1", "pinia-plugin-persist": "^1.0.0", "pixi.js": "8.5.1", "pixijs": "7.1.4", "sdpc-web-ts": "git+http://**************/hugc/sdpc-web-ts.git", "uuid": "^11.0.5", "vue": "^3.2.13", "vue-router": "^4.0.3"}, "devDependencies": {"@types/lodash": "^4.17.7", "@types/lodash.merge": "^4.6.7", "@types/uuid": "^8.3.3", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^7.32.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^8.0.3", "pont-engine": "git+http://**************/hugc/pont.git", "postcss-px-to-viewport-8-plugin": "^1.1.5", "prettier": "^2.4.1", "rimraf": "^3.0.2", "sass": "^1.32.7", "sass-loader": "^12.0.0", "typescript": "~4.5.5", "unplugin-auto-import": "^0.12.1", "unplugin-vue-components": "^0.22.12"}}