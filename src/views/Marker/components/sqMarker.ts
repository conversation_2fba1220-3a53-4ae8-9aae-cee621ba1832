/**
 * 标注组件入口
 */
import { KonvaOverlay, Layer, Stage } from './konvaOverlayer';
import OpenSeadragon from 'openseadragon';
import { DrawType } from './editorEnum';
import { Editor } from './editor';
import { RectConfig } from 'konva/lib/shapes/Rect';
import { ImageConfig } from 'konva/lib/shapes/Image';
import { CircleConfig } from 'konva/lib/shapes/Circle';
import { ArrowConfig } from 'konva/lib/shapes/Arrow';
import { EllipseConfig } from 'konva/lib/shapes/Ellipse';
import { LineConfig } from 'konva/lib/shapes/Line';
import { RingConfig } from 'konva/lib/shapes/Ring';
import Label from './label';
import { localJSONToRadingSoftwareJSON, RadingSoftwareJSONTolocalJSON } from '@/utils/JSON';

interface SQMarkerOptions {
  needDrawingDash?: boolean; // 画图中是否需要虚线
  drawingDash?: number[]; // 虚线配置
  dblclickClose?: boolean; // 是否需要双击退出编辑模式
  drawEndCallback?: Function;
  needCountArea?: boolean; // 是否需要面积/周长计算
}

class SQMarker {
  protected _konvaOverlay: KonvaOverlay;
  protected _konveEditor: Editor;
  protected _konveLayer: Layer;
  protected _konvaStage: Stage;
  protected _globalOptions: SQMarkerOptions;
  protected _label: Label;
  static Label: Label;
  get label(): Label {
    return this._label;
  }
  get editor(): Editor {
    return this._konveEditor;
  }

  defaultOptions = {
    needDrawingDash: true,
    drawingDash: [10, 5],
    drawEndCallback: () => {
      console.log('drawEndCallback');
    },
    needCountArea: false,
  };

  constructor(viewer: OpenSeadragon.Viewer, options: SQMarkerOptions = {}) {
    this._konvaOverlay = new KonvaOverlay(viewer);
    this._konveLayer = this._konvaOverlay.layer;
    this._label = new Label();
    this._konvaStage = this._konvaOverlay.konvaStage;
    this._konveEditor = new Editor(viewer, this._konveLayer, {
      needCountArea: options.needCountArea,
      Label: this._label,
    });
    this._globalOptions = {
      ...this.defaultOptions,
      ...options,
    };
  }

  handleShapeConfig(shapeConfig: any) {
    return {
      ...shapeConfig,
      dash: this._globalOptions?.drawingDash || [],
    };
  }

  setRatio(scale: any) {
    if (!scale) return;
    let scales: number = scale;
    this._konveEditor.setRatio(scale);
  }

  drawLine(options?: RectConfig) {
    this._konveEditor.setDrawingOption(this.handleShapeConfig(options));
    this._konveEditor.start(DrawType.line);
  }

  drawRect(options?: RectConfig) {
    this._konveEditor.setDrawingOption(this.handleShapeConfig(options));
    this._konveEditor.start(DrawType.rect);
  }

  drawArrow(options?: Partial<ArrowConfig>) {
    this._konveEditor.setDrawingOption(this.handleShapeConfig(options));
    this._konveEditor.start(DrawType.arrow);
  }

  drawEllipse(options?: Partial<EllipseConfig>) {
    this._konveEditor.setDrawingOption(this.handleShapeConfig(options));
    this._konveEditor.start(DrawType.ellipse);
  }

  drawFlag(options?: any) {
    this._konveEditor.setDrawingOption(options);
    this._konveEditor.start(DrawType.flag);
  }

  drawCircle(options?: CircleConfig) {
    this._konveEditor.setDrawingOption(this.handleShapeConfig(options));
    this._konveEditor.start(DrawType.circle);
  }

  drawAngle(options?: LineConfig) {
    this._konveEditor.setDrawingOption(this.handleShapeConfig(options));
    this._konveEditor.start(DrawType.angle);
  }

  drawFoldLine(options?: LineConfig) {
    this._konveEditor.setDrawingOption(this.handleShapeConfig(options));
    this._konveEditor.start(DrawType.foldLine);
  }

  drawRing(options?: any) {
    this._konveEditor.setDrawingOption(this.handleShapeConfig(options));
    this._konveEditor.start(DrawType.ring);
  }

  drawPolygon(options?: LineConfig) {
    this._konveEditor.setDrawingOption(this.handleShapeConfig(options));
    this._konveEditor.start(DrawType.polygon);
  }

  drawClosedCurve(options?: LineConfig) {
    this._konveEditor.setDrawingOption(this.handleShapeConfig(options));
    this._konveEditor.start(DrawType.closedCurve);
  }

  drawFreeCurve(options?: LineConfig) {
    this._konveEditor.setDrawingOption(this.handleShapeConfig(options));
    this._konveEditor.start(DrawType.freeCurve);
  }

  drawRuler(options?: LineConfig) {
    this._konveEditor.setDrawingOption(this.handleShapeConfig(options));
    this._konveEditor.start(DrawType.ruler);
  }

  drawWand(options?: any) {
    // 合并魔杖特定的配置参数
    const wandOptions = {
      ...this.handleShapeConfig(options),
      // 魔杖工具特有的配置参数
      baseBufferSize: options?.baseBufferSize,
      baseSensitivity: options?.baseSensitivity,
      mode: options?.mode,
    };
    this._konveEditor.setDrawingOption(wandOptions);
    this._konveEditor.start(DrawType.wand);
  }

  setDrawingOption(options: any) {
    this._konveEditor.setDrawingOption(options);
  }

  showKonva() {
    this._konvaStage.show();
  }
  hideKonva() {
    this._konvaStage.hide();
    this._konveEditor.close();
  }

  localJSONToRadingSoftwareJSON(labels: any[]) {
    return localJSONToRadingSoftwareJSON(labels);
  }
  RadingSoftwareJSONTolocalJSON(viewer: any, data: string) {
    return RadingSoftwareJSONTolocalJSON(viewer, data);
  }
}

export default SQMarker;
