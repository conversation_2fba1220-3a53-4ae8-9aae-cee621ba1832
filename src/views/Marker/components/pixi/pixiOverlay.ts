// pixi的图层
import OpenSeadragon, { MouseTracker, Point } from 'openseadragon';
import * as PIXI from 'pixijs';
import { Application, Container, Rectangle } from 'pixi.js';
import { AreasMap } from './pixi.types';
interface Viewport extends OpenSeadragon.Viewport {
  _containerInnerSize: { x: number; y: number };
  _contentSize: { x: number; y: number };
}

interface Options {
  showAll?: boolean; // 是否绘制结束之后展示所有区域
  pixiIsV8?: boolean; // 是否启用8版本
}

export default class PixiOverlay {
  protected _viewer: OpenSeadragon.Viewer;
  protected _canvas!: HTMLCanvasElement;
  protected _containerWidth: number = 0;
  protected _defaultContainerWidth: number = 0; //第一次加载切片，容器的宽度
  protected _containerHeight: number = 0;
  // protected _id: string = 'pixi-overlay';
  protected _id: string = 'AI-overlay';
  protected _scale: number = 0;
  protected _mouseTracker!: MouseTracker;
  protected _app: Application | PIXI.Application;
  container: Container | PIXI.Container;
  protected _drawDoneCallback: any;
  protected _options: Options;
  areasMap: AreasMap = {};
  //openseadrogen Viewer对象
  get viewer() {
    return this._viewer;
  }

  //openseadrogen viewport对象
  get viewport(): Viewport {
    return this._viewer?.viewport as Viewport;
  }

  get canvasDiv(): HTMLCanvasElement {
    return this._canvas;
  }

  constructor(viewer: OpenSeadragon.Viewer, options: Options) {
    this._viewer = viewer;
    this._containerWidth = this._viewer.container.clientWidth;
    this._containerHeight = this._viewer.container.clientHeight;
    this._defaultContainerWidth = this.viewport._containerInnerSize.x;
    this._options = options;
    this.createPixis();
    this.resize();
    this._viewer.addHandler('update-viewport', (ev) => {
      this.resize();
    });
  }

  //
  destroy() {
    this.container?.destroy(true);
    this.canvasDiv.remove();
  }

  //创建画图层div
  createCanvas() {
    // const imageBrowser = document.getElementById('image-broswer-main');
    // const konvaOverlay = imageBrowser?.querySelector(`#${this._id}`);
    const konvaOverlay = this._viewer.canvas.querySelector(`#${this._id}`);
    if (konvaOverlay) {
      this._canvas = konvaOverlay as HTMLCanvasElement;
    } else {
      this._canvas = document.createElement('canvas');
      this._canvas.style.position = 'absolute';
      this._canvas.style.left = '0';
      this._canvas.style.top = '0';
      this._canvas.style.width = '100%';
      this._canvas.style.height = '100%';
      this._canvas.style.pointerEvents = 'none';
      this._canvas.style.zIndex = '2';
      this._canvas.setAttribute('id', this._id);
      this._viewer.canvas.appendChild(this._canvas);
    }
  }

  //设置pixi对象
  createPixis() {
    this.createCanvas();
    if (this._options?.pixiIsV8) {
      this._app = new Application();
      (async () => {
        await (this._app as Application).init({
          canvas: this._canvas,
          width: this._canvas.width,
          height: this._canvas.height,
          resizeTo: this._viewer.container,
          autoDensity: true,
          backgroundAlpha: 0,
          antialias: true,
        });
        this.container = new Container();
        (this._app as Application).stage.addChild(this.container);
        this.container.cursor = 'pointer';
        this.container.sortableChildren = true;
        this.container.interactiveChildren = false;
        this.container.hitArea = new Rectangle(
          0,
          0,
          (this._app as Application).renderer.canvas.width,
          (this._app as Application).renderer.canvas.height
        );
        console.log('await');
      })();
    } else {
      this._app = new PIXI.Application({
        view: this._canvas,
        width: this._canvas.width,
        height: this._canvas.height,
        resizeTo: this._viewer.container,
        autoDensity: true,
        backgroundAlpha: 0,
        antialias: true,
      });
      this.container = new PIXI.Container();
      this._app.stage.addChild(this.container);
      this.container.cursor = 'pointer';
      this.container.sortableChildren = true;
      this.container.interactiveChildren = false;
      this.container.hitArea = new Rectangle(
        0,
        0,
        this._app.renderer.view.width,
        this._app.renderer.view.height
      );
      this.container.filters = null;
    }
  }

  //视图大小调整
  resize() {
    const viewer = this._viewer;
    const viewport = this._viewer.viewport as Viewport;
    const app = this._app;
    var p = viewport.pixelFromPoint(new OpenSeadragon.Point(0, 0), true);
    const zoom = (viewport._containerInnerSize.x / 1000) * viewport.getZoom(true);
    const rotation = viewer.viewport.getRotation();
    if (!app.stage) return;
    app.stage.scale.set(zoom, zoom);
    app.stage.position.set(p.x, p.y);
    app.stage.rotation = rotation * (Math.PI / 180);
    app.render();

    // 重新渲染热力图以适应新的视口
    this._renderHeatmapInternal();
  }

  private _positionGraphicsId: string = '';
  private _positionGraphics: any = null;
  positioningOSDByPixiPoint(
    x: number,
    y: number,
    rectWidth = 100,
    rectHeight = 100,
    needSuitableZoom = true
  ) {
    const id = `${x}${y}${rectWidth}${rectHeight}`;
    if (this._positionGraphicsId === id && this._positionGraphics) {
      this._positionGraphicsId = '';
      this._positionGraphics.destroy();
      this._positionGraphics = null;
      return;
    }
    if (this._positionGraphics) {
      this._positionGraphics.destroy();
      this._positionGraphics = null;
    }

    // x y 转成视图坐标
    const panPoint = this.viewer.viewport.imageToViewportCoordinates(
      x + rectWidth / 2,
      y + rectHeight / 2
    );
    this.viewer.viewport.panTo(panPoint);

    // 转换宽高
    const point = this.viewer.viewport.imageToViewportCoordinates(x, y);
    const resultWidth =
      this.viewer.viewport.imageToViewportCoordinates(x + rectWidth, y).x - point.x;
    const resultHeight =
      this.viewer.viewport.imageToViewportCoordinates(x, y + rectHeight).y - point.y;
    this._positionGraphics = new PIXI.Graphics();
    this._positionGraphicsId = id;
    this._positionGraphics.beginFill(0xff0044, 0.1);
    this._positionGraphics.lineStyle({
      color: 16711680,
      width: 0.1,
    });
    this._positionGraphics.drawRect(
      point.x * 1000,
      point.y * 1000,
      resultWidth * 1000,
      resultHeight * 1000
    );
    (this.container as any).addChild(this._positionGraphics as any);

    if (needSuitableZoom) {
      // 获取图像最大宽高
      const tiledImage = this._viewer.world.getItemAt(0); // 假设只有一张图像
      const { x: maxWidth, y: maxHeight } = tiledImage.getContentSize(); // 图像的实际尺寸（宽和高）
      // const
      const minViewport = Math.min(maxWidth, maxHeight);
      const minRect = Math.min(resultWidth * 1000, resultHeight * 1000);
      const maxZoom = this.viewer.viewport.getMaxZoom();
      const minZoom = this.viewer.viewport.getMinZoom();
      const zoom = minViewport / minRect / 100;
      if (zoom > maxZoom) {
        this.viewer.viewport.zoomTo(maxZoom);
      } else if (zoom < minZoom) {
        this.viewer.viewport.zoomTo(minZoom);
      } else {
        this.viewer.viewport.zoomTo(zoom);
      }
    }
  }

  private _infoGraphics: any = null;
  private _infoGraphicsId: string = '';
  showHKInfo(infoParams: {
    x: number;
    y: number;
    rectWidth: number;
    rectHeight: number;
    stringArray: string[];
  }) {
    // 在矩形上添加info展示框
    const { x, y, rectWidth = 100, rectHeight = 100, stringArray = [] } = infoParams;
    const id = `${x}${y}${rectWidth}${rectHeight}`;
    if (this._infoGraphics && this._infoGraphicsId === id) {
      this._infoGraphics.destroy();
      this._infoGraphics = null;
      this._infoGraphicsId = '';
      return;
    }
    if (this._infoGraphics) {
      this._infoGraphics.destroy();
      this._infoGraphics = null;
    }

    // 转换宽高
    this._infoGraphicsId = id;
    const point = this.viewer.viewport.imageToViewportCoordinates(x, y);
    const resultWidth =
      this.viewer.viewport.imageToViewportCoordinates(x + rectWidth, y).x - point.x;
    const resultHeight =
      this.viewer.viewport.imageToViewportCoordinates(x, y + rectHeight).y - point.y;

    this._infoGraphics = new PIXI.Graphics();
    this._infoGraphics.beginFill(0, 0.5);
    this._infoGraphics.drawRect(
      point.x * 1000 - resultWidth * 1000 * 0.2,
      (point.y - 0.6 * resultHeight) * 1000,
      resultWidth * 1000 * 1.4,
      resultHeight * 1000 * 0.5
    );
    // this._infoGraphics.width = resultWidth * 1000;
    // this._infoGraphics.height = resultHeight * 1000 * 0.3;
    (this.container as any).addChild(this._infoGraphics as any);
    if (stringArray.length > 0) {
      const resultString = stringArray.reduce((acc, cur) => {
        return `${acc}\n${cur}`;
      }, '');
      const fontSize = resultHeight * 1000 * 0.5 * 0.4;
      const text = new PIXI.Text(resultString, {
        fontSize: 64,
        fill: 0xffffff,
        align: 'center',
      });
      text.anchor.set(0.5, 0);
      text.scale.set(fontSize / 64);
      (this._infoGraphics as any).addChild(text);

      text.x = point.x * 1000 + (resultWidth * 1000) / 2;
      text.y = (point.y - 0.6 * resultHeight) * 1000 - (resultHeight * 1000 * 0.5) / 2;
    }
  }

  // 通过data.value里面的id去查nameList里面对应的name,然后把namepush进data.value

  // data.value.map(itemX => {
  //   return {
  //     ...itemX,
  //     name: nameList.value.find(itemY = > {
  //       itemY.id === itemX.adv_activity_id
  //     })?.name
  //   }
  // })

  getPointFromPixi(point: { x: number; y: number }) {
    const { x, y } = point;
    // x = (pixel.x - pixelNoRotate.x) / this.layerScale  --- 这是将鼠标坐标转为Konva坐标点的公式
    // 求pixel.x公式：pixel.x = x * scale + pixelNoRotate.x
    const pixelNoRotate = this._viewer.viewport.pixelFromPointNoRotate(
      new OpenSeadragon.Point(0, 0),
      true
    );
    const scaleX = this._app.stage.scale.x;
    const scaleY = this._app.stage.scale.y;
    const pixelX = x * scaleX + pixelNoRotate.x;
    const pixelY = y * scaleY + pixelNoRotate.y;
    return this._viewer.viewport.pointFromPixel(new Point(pixelX, pixelY));
    // ！重点是算出形状在OSD上的Point
  }

  showContainer() {
    this.container.visible = true;
  }
  hideContainer() {
    this.container.visible = false;
  }

  // 热力图相关方法
  private _heatmapContainer: any = null;
  private _heatmapData: Array<{ x: number; y: number; intensity: number }> = [];
  private _heatmapOptions: {
    radius?: number;
    intensity?: number;
    opacity?: number;
    colorScheme?: string;
    blur?: number;
  } = {};

  createHeatmapContainer() {
    if (this._heatmapContainer) {
      (this.container as any).removeChild(this._heatmapContainer);
      this._heatmapContainer.destroy();
    }

    if (this._options?.pixiIsV8) {
      this._heatmapContainer = new Container();
    } else {
      this._heatmapContainer = new PIXI.Container();
    }

    (this.container as any).addChild(this._heatmapContainer);
    return this._heatmapContainer;
  }

  renderHeatmapPoints(
    points: Array<{ x: number; y: number; intensity: number }>,
    options: {
      radius?: number;
      intensity?: number;
      opacity?: number;
      colorScheme?: string;
      blur?: number;
    } = {}
  ) {
    // // 将 OpenSeadragon 像素坐标转换为相对坐标 (0-1 范围)
    // const convertedPoints = points.map((point) => {
    //   // 将像素坐标转换为视口坐标
    //   const viewportPoint = this._viewer.viewport.pointFromPixel(
    //     new OpenSeadragon.Point(point.x, point.y)
    //   );

    //   // 再转换为图像坐标
    //   let imagePoint = null;
    //   const imageItem = this._viewer.world.getItemAt(0);
    //   if (imageItem) {
    //     imagePoint = imageItem.viewportToImageCoordinates(viewportPoint.x, viewportPoint.y);
    //   } else {
    //     imagePoint = this._viewer.viewport.viewportToImageCoordinates(viewportPoint);
    //   }

    //   // 获取图像尺寸来计算相对坐标
    //   const imageSize = imageItem ? imageItem.getContentSize() : { x: 1, y: 1 };

    //   return {
    //     x: imagePoint.x / imageSize.x, // 转换为 0-1 相对坐标
    //     y: imagePoint.y / imageSize.y, // 转换为 0-1 相对坐标
    //     intensity: point.intensity,
    //   };
    // });

    // 保存转换后的热力图数据和选项，用于视口变化时重新渲染
    this._heatmapData = points;
    this._heatmapOptions = { ...options };

    this._renderHeatmapInternal();
  }

  private _renderHeatmapInternal() {
    if (this._heatmapData.length === 0) return;

    const {
      radius = 50,
      intensity = 1.0,
      opacity = 0.8,
      colorScheme = 'heat',
      blur = 15,
    } = this._heatmapOptions;

    if (!this._heatmapContainer) {
      this.createHeatmapContainer();
    }

    // 清除之前的热点
    this._heatmapContainer.removeChildren();

    // 使用 Canvas 方式创建真正的热力图
    const heatmapTexture = this.createCanvasHeatmap(
      this._heatmapData,
      radius,
      intensity,
      opacity,
      colorScheme,
      blur
    );

    // 创建精灵显示热力图
    let sprite: any;
    if (this._options?.pixiIsV8) {
      sprite = new (PIXI as any).Sprite(heatmapTexture);
    } else {
      sprite = new PIXI.Sprite(heatmapTexture);
    }

    this._heatmapContainer.addChild(sprite);
  }

  private createCanvasHeatmap(
    points: Array<{ x: number; y: number; intensity: number }>,
    radius: number,
    globalIntensity: number,
    opacity: number,
    colorScheme: string,
    blur: number
  ): any {
    // 获取容器尺寸
    const width = this._containerWidth || 1000;
    const height = this._containerHeight || 1000;

    // 获取当前缩放级别
    const viewport = this._viewer.viewport as Viewport;
    const currentZoom = viewport.getZoom(true);

    // 计算缩放因子 - 缩小时热点合并（半径增大），放大时热点分开（半径减小）
    const baseZoom = 1.0; // 基准缩放级别
    let zoomFactor: number;

    if (currentZoom < baseZoom) {
      // 缩小时：增大半径和模糊，让热点合并
      zoomFactor = Math.max(0.3, 1.0 / currentZoom);
    } else {
      // 放大时：减小半径和模糊，让热点分开
      zoomFactor = Math.max(0.2, 1.0 / Math.sqrt(currentZoom));
    }

    const adjustedRadius = radius * zoomFactor;
    const adjustedBlur = blur * zoomFactor;

    // 调试信息
    console.log(
      `热力图缩放调整: zoom=${currentZoom.toFixed(2)}, factor=${zoomFactor.toFixed(
        2
      )}, radius=${adjustedRadius.toFixed(1)}, blur=${adjustedBlur.toFixed(1)}`
    );

    // 创建临时 Canvas
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d')!;

    // 绘制所有热点（使用白色，强度作为alpha）
    points.forEach((point) => {
      const x = point.x * width;
      const y = point.y * height;
      const pointIntensity = point.intensity * globalIntensity;

      this.drawCanvasHeatPoint(ctx, x, y, adjustedRadius, pointIntensity);
    });

    // 应用模糊效果
    if (adjustedBlur > 0) {
      ctx.filter = `blur(${adjustedBlur}px)`;
      ctx.drawImage(canvas, 0, 0);
      ctx.filter = 'none';
    }

    // 应用颜色映射
    this.applyCanvasColorMapping(ctx, width, height, colorScheme, opacity);

    // 将 Canvas 转换为 PIXI 纹理
    if (this._options?.pixiIsV8) {
      return (PIXI as any).Texture.from(canvas);
    } else {
      return PIXI.Texture.from(canvas);
    }
  }

  private drawCanvasHeatPoint(
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    radius: number,
    intensity: number
  ) {
    // 创建径向渐变
    const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
    gradient.addColorStop(0, `rgba(255, 255, 255, ${intensity})`);
    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fill();
  }

  private applyCanvasColorMapping(
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number,
    colorScheme: string,
    opacity: number
  ) {
    const imageData = ctx.getImageData(0, 0, width, height);
    const data = imageData.data;

    const colorSchemes = this.getCanvasColorSchemes();
    const colors = (colorSchemes as any)[colorScheme] || colorSchemes.heat;

    // 处理每个像素
    for (let i = 0; i < data.length; i += 4) {
      const alpha = data[i + 3] / 255; // 使用 alpha 通道作为强度值

      if (alpha > 0) {
        const color = this.interpolateCanvasColor(colors, alpha);
        data[i] = color.r; // Red
        data[i + 1] = color.g; // Green
        data[i + 2] = color.b; // Blue
        data[i + 3] = color.a * opacity * 255; // Alpha
      }
    }

    // 将处理后的图像数据绘制回画布
    ctx.putImageData(imageData, 0, 0);
  }

  private getCanvasColorSchemes() {
    return {
      heat: [
        { pos: 0.0, r: 0, g: 0, b: 0, a: 0 },
        { pos: 0.2, r: 0, g: 0, b: 255, a: 0.8 },
        { pos: 0.4, r: 0, g: 255, b: 255, a: 0.9 },
        { pos: 0.6, r: 0, g: 255, b: 0, a: 1.0 },
        { pos: 0.8, r: 255, g: 255, b: 0, a: 1.0 },
        { pos: 1.0, r: 255, g: 0, b: 0, a: 1.0 },
      ],
    };
  }

  private interpolateCanvasColor(colors: any[], value: number) {
    value = Math.max(0, Math.min(1, value));

    // 找到相邻的两个颜色点
    let lower = colors[0];
    let upper = colors[colors.length - 1];

    for (let i = 0; i < colors.length - 1; i++) {
      if (value >= colors[i].pos && value <= colors[i + 1].pos) {
        lower = colors[i];
        upper = colors[i + 1];
        break;
      }
    }

    // 计算插值比例
    const range = upper.pos - lower.pos;
    const ratio = range === 0 ? 0 : (value - lower.pos) / range;

    // 线性插值
    return {
      r: Math.round(lower.r + (upper.r - lower.r) * ratio),
      g: Math.round(lower.g + (upper.g - lower.g) * ratio),
      b: Math.round(lower.b + (upper.b - lower.b) * ratio),
      a: lower.a + (upper.a - lower.a) * ratio,
    };
  }

  clearHeatmap() {
    this._heatmapData = [];
    this._heatmapOptions = {};
    if (this._heatmapContainer) {
      this._heatmapContainer.removeChildren();
    }
  }
}
