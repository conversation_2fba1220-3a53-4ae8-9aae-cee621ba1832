<template>
  <div style="position: absolute; top: 150px; left: 250px; z-index: 999">
    <button @click="showSomeAreas">AI区域切换</button>
    <button @click="handlePosition">定位</button>
    <button @click="toggleHeatmap">{{ heatmapVisible ? '隐藏热力图' : '显示热力图' }}</button>
    <button @click="generateHeatmapData">生成热力图数据</button>
    <button @click="clearHeatmap">清除热力图</button>
  </div>

  <!-- 热力图控制面板 -->
  <div v-if="heatmapVisible"
    style="position: absolute; top: 200px; left: 250px; z-index: 999; background: rgba(255,255,255,0.9); padding: 15px; border-radius: 8px; min-width: 200px;">
    <h4 style="margin: 0 0 10px 0; color: #333;">热力图控制</h4>

    <div style="margin-bottom: 10px;">
      <label style="display: block; margin-bottom: 5px; font-size: 12px;">半径: {{ heatmapOptions.radius }}</label>
      <input type="range" min="10" max="100" v-model="heatmapOptions.radius" @input="updateHeatmap"
        style="width: 100%;" />
    </div>

    <div style="margin-bottom: 10px;">
      <label style="display: block; margin-bottom: 5px; font-size: 12px;">强度: {{ heatmapOptions.intensity }}</label>
      <input type="range" min="0.1" max="2" step="0.1" v-model="heatmapOptions.intensity" @input="updateHeatmap"
        style="width: 100%;" />
    </div>

    <div style="margin-bottom: 10px;">
      <label style="display: block; margin-bottom: 5px; font-size: 12px;">透明度: {{ heatmapOptions.opacity }}</label>
      <input type="range" min="0.1" max="1" step="0.1" v-model="heatmapOptions.opacity" @input="updateHeatmap"
        style="width: 100%;" />
    </div>

    <div style="margin-bottom: 10px;">
      <label style="display: block; margin-bottom: 5px; font-size: 12px;">模糊: {{ heatmapOptions.blur }}</label>
      <input type="range" min="0" max="30" step="1" v-model="heatmapOptions.blur" @input="updateHeatmap"
        style="width: 100%;" />
    </div>



    <div style="font-size: 12px; color: #666;">
      数据点: {{ heatmapStats.pointCount }}<br>
      平均强度: {{ heatmapStats.avgIntensity.toFixed(2) }}<br>
      最大强度: {{ heatmapStats.maxIntensity.toFixed(2) }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, reactive } from 'vue';
import PixiAIMarker from './index';
import OpenSeadragon from 'openseadragon';

// 热力图相关接口
interface HeatmapPoint {
  x: number;
  y: number;
  intensity: number;
}

interface HeatmapOptions {
  radius: number;
  intensity: number;
  opacity: number;
  blur: number;
}

interface HeatmapStats {
  pointCount: number;
  avgIntensity: number;
  maxIntensity: number;
}

const props = defineProps({
  viewer: OpenSeadragon.Viewer,
  flip: Boolean,
});

// 热力图状态
const heatmapVisible = ref(false);
const heatmapData = ref<HeatmapPoint[]>([]);
const heatmapOptions = reactive<HeatmapOptions>({
  radius: 30,
  intensity: 1.0,
  opacity: 0.8,
  blur: 15
});

const heatmapStats = reactive<HeatmapStats>({
  pointCount: 0,
  avgIntensity: 0,
  maxIntensity: 0
});



// const fetchAIUrl = async () => {
//   axios({
//     url: `http://**************:7015/api/slice-inner-ai-label/${encodeURIComponent(
//       `B202210036-004-ihc`
//     )}/Ai-result`,
//     method: 'get',
//   }).then((res: any) => {
//     console.log(res.data.data, 'res1');
//     aiUrl.value = res.data.data;
//     fetchAI();
//   });
// };

// const fetchAI = () => {
// axios({
//   url: aiUrl.value,
//   method: 'get',
//   // params: { sliceName },
// }).then((res: any) => {
//   aiResult.value = res.data;
//   pixi.value?.addMarkerByGroupModel(res.data.GroupModel)
// });
// };

const handlePosition = () => {
  console.log(pixi.value, 'pixi.value');
  pixi.value?.pixiOverlay.positioningOSDByPixiPoint(35072 / 150, 101632 / 150);
  pixi.value?.pixiOverlay.showHKInfo({
    x: 35072 / 150,
    y: 101632 / 150,
    rectWidth: 100,
    rectHeight: 100,
    stringArray: ['asc_us', '111211231232131231um²'],
  });
};
const fetchJsonData = () => {
  // fetch('./jsonData/mianzizuhua/B202210036-004ER.json')
  // fetch('./jsonData/mianzizuhua/S111.json')
  // fetch('./jsonData/mianzizuhua/*********-P16.json')
  // fetch('./jsonData/mianzizuhua/B202215036-002HER-2.json')
  // fetch('./jsonData/lymphnode/2024101501.json');
  // fetch('./jsonData/stomach/D201900138-001.json')
  // fetch('./jsonData/er/B202210036-004ER.json')
  // fetch('./jsonData/KI67/Q22.json')
  // fetch('./jsonData/KI67/B202210469-027ki-67.json')
  // fetch('./jsonData/intestine/Q24-05766.json') 
  // fetch('./jsonData/tumor/tumor2_tumor_regions.json') 
  // fetch('./jsonData/gongjin/**********-1.json') 
  // fetch('./jsonData/other/hk1.json') 
  //   .then((response) => {
  //     console.log(response, 'response');
  //     return response.json();
  //   })
  //   .then((data) => {
  //     console.log(data, 'datadatadata');
  //     // pixi.value?.pixiMarker.addMarkersByGroupModel(data.GroupModel);
  //     // pixi.value?.pixiMarker.addLymMarkersByGroupModel(data.GroupModel);
  //     // pixi.value?.pixiMarker.addStomachMarkersByGroupModel(data.GroupModel);
  //     // pixi.value?.pixiMarker.addIntestineMarkersByGroupModel(data.GroupModel);
  //     console.log(pixi.value?.pixiMarker.addHKMarkersByGroupModel(data.GroupModel), 'labels')
  //   });
};

const pixi = ref<PixiAIMarker | null>(null);
onMounted(async () => {
  if (props.viewer) {
    pixi.value = new PixiAIMarker(props.viewer, {
      showAll: true,
      pixiIsV8: false,
    });
  }
  // await fetchAIUrl();
  setTimeout(() => {
    fetchJsonData();
    // 初始化热力图数据
    generateHeatmapData();
  }, 2000);
});
const showSomeAreas = () => {
  pixi.value?.pixiMarker.showAreas(['ROI']);
  // pixi.value?.pixiMarker.showAreasByIds([1]);
};

// 热力图方法
const toggleHeatmap = () => {
  heatmapVisible.value = !heatmapVisible.value;
  if (heatmapVisible.value) {
    renderHeatmap();
  } else {
    clearHeatmapRender();
  }
};

const generateHeatmapData = () => {
  const points: HeatmapPoint[] = [];

  // 生成聚类数据
  const clusterCount = 5;
  for (let c = 0; c < clusterCount; c++) {
    const centerX = Math.random();
    const centerY = Math.random();
    const clusterSize = Math.floor(Math.random() * 15) + 5;

    for (let i = 0; i < clusterSize; i++) {
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * 0.1;

      points.push({
        x: Math.max(0, Math.min(1, centerX + Math.cos(angle) * distance)),
        y: Math.max(0, Math.min(1, centerY + Math.sin(angle) * distance)),
        intensity: Math.random() * 0.8 + 0.2
      });
    }
  }
  console.log(points, 'points');
  heatmapData.value = points;
  updateHeatmapStats();
  if (heatmapVisible.value) {
    renderHeatmap();
  }
};

const clearHeatmap = () => {
  heatmapData.value = [];
  updateHeatmapStats();
  clearHeatmapRender();
};

const updateHeatmap = () => {
  if (heatmapVisible.value) {
    renderHeatmap();
  }
};

const updateHeatmapStats = () => {
  const pointCount = heatmapData.value.length;
  const avgIntensity = pointCount > 0 ?
    heatmapData.value.reduce((sum, p) => sum + p.intensity, 0) / pointCount : 0;
  const maxIntensity = pointCount > 0 ?
    Math.max(...heatmapData.value.map(p => p.intensity)) : 0;

  heatmapStats.pointCount = pointCount;
  heatmapStats.avgIntensity = avgIntensity;
  heatmapStats.maxIntensity = maxIntensity;
};

// 热力图渲染方法
const renderHeatmap = () => {
  if (!pixi.value?.pixiOverlay) return;

  pixi.value.pixiOverlay.renderHeatmapPoints(heatmapData.value, {
    radius: heatmapOptions.radius,
    intensity: heatmapOptions.intensity,
    opacity: heatmapOptions.opacity,
    colorScheme: 'heat', // 固定使用经典热力图配色
    blur: heatmapOptions.blur
  });
};

const clearHeatmapRender = () => {
  if (pixi.value?.pixiOverlay) {
    pixi.value.pixiOverlay.clearHeatmap();
  }
};


watch(
  () => props.flip,
  (value) => {
    // if (value) {
    pixi.value?.pixiMarker.setFlip(value);
    // }
  }
);
</script>

<style lang="scss"></style>
