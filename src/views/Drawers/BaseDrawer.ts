import { Layer } from 'konva/lib/Layer';
import { Shape } from 'konva/lib/Shape';
import Konva from 'konva';
import OpenSeadragon, { Point } from 'openseadragon';
import { DrawStatus } from '@/views/Marker/components/editorEnum';
import mitt, { type Emitter } from 'mitt';
import { polygonArea } from 'geometric';
import { PointPerimeterImagePostion, OpenSeadragonRatio, PerimeterScale } from './Drawer.types.js';
import { viewCoordinatesToImageCoordinates as VCToIC } from '@/utils/coordinate';
export interface DrawPoint {
  x: number;
  y: number;
  imageX?: number;
  imageY?: number;
  pcx?: number;
  pcy?: number;
}

export abstract class BaseDrawer<T extends Shape | Konva.Group> {
  protected node: T | null = null;
  protected layer: Layer;
  protected viewer: OpenSeadragon.Viewer;
  protected config: any = {};
  protected startPoint: any = {};
  protected status: DrawStatus = DrawStatus.end;
  protected minWidth = 0.01;
  protected minHeight = 0.01;
  protected _ratio: OpenSeadragonRatio = { unit: 'px', scales: 1 };
  protected emitter: Emitter<any> = mitt();
  protected on: Emitter<any>['on'];
  protected off: Emitter<any>['off'];
  protected emit: Emitter<any>['emit'];

  //缩放大小
  get layerScale(): number {
    return this.layer ? this.layer.scaleX() : 1;
  }

  get selectNode() {
    // console.log(this.layer)
    const childs = this.layer.getChildren((node: any) => {
      const { isSelect } = node.attrs;
      return isSelect;
    });
    if (childs.length > 1) {
      console.warn('存在多个选中状态的node', childs);
      return childs[0];
    } else {
      return childs[0];
    }
  }

  get sceneFuncNeedToRecalc() {
    return this.selectNode;
  }

  get viewCoordinatesToImageCoordinates() {
    return VCToIC;
  }

  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, config: any) {
    this.layer = layer;
    this.viewer = viewer;
    this.config = config || {};
    this.on = this.emitter.on.bind(this.emitter);
    this.off = this.emitter.off.bind(this.emitter);
    this.emit = this.emitter.emit.bind(this.emitter);
  }

  // 开始绘画
  start() {
    console.log('go start');
    this.status = DrawStatus.drawing;
  }

  // 结束绘画
  end() {
    this.status = DrawStatus.end;
    if (this.node) {
      this.node.setAttrs({
        dash: [],
      });
      // this.updateHistory();
    }
    // this.node = null;
    // this.startPoint = {};
    this.emit('drawingEnd', {
      node: this.node,
    });
  }

  nodeDestroy() {
    this.node?.remove();
    this.node?.destroy();
    this.layer.draw();
    this.node = null;
    // this.startPoint = {};
    this.status = DrawStatus.end;
  }

  _konvaPointFromPixl(x: number, y: number) {
    const viewportPoint = this.viewer.viewport.pointFromPixel(new Point(x, y));
    let imagePoint = null;
    if (process.env.NODE_ENV === 'development') {
      imagePoint = this.viewer.viewport.viewportToImageCoordinates(viewportPoint);
    } else {
      imagePoint = this.viewer.world
        .getItemAt(0)
        .viewportToImageCoordinates(viewportPoint.x, viewportPoint.y);
    }

    // 左上坐标
    const pixelNoRotate = this.viewer.viewport.pixelFromPointNoRotate(
      new OpenSeadragon.Point(0, 0),
      true
    );
    const point = this.viewer.viewport.pointFromPixel(new Point(x, y));
    const pixel = this.viewer.viewport.pixelFromPointNoRotate(point); //当前坐标转化为旋转度数为0度的实际坐标
    return {
      x: (pixel.x - pixelNoRotate.x) / this.layerScale,
      y: (pixel.y - pixelNoRotate.y) / this.layerScale,
      imageX: imagePoint.x,
      imageY: imagePoint.y,
      dx: x,
      dy: y,
    };
  }

  get curPoint() {
    return this.getCurPoint();
  }
  protected getCurPoint() {
    const touchPos = this.layer.getStage().getPointerPosition();
    const x = (touchPos?.x as number) || 0;
    const y = (touchPos?.y as number) || 0;
    const isFlipped = document
      .getElementById('konva-overlay')
      ?.style.transform.includes('scaleX(-1)');
    let adjustedX = x;
    if (isFlipped) {
      const stageWidth = this.layer.getStage().width();
      adjustedX = stageWidth - x; // 进行水平翻转
    }
    // console.log(touchPos, adjustedX, y, 'adjustedX, y');
    const curPoint = this._konvaPointFromPixl(adjustedX, y);
    return curPoint;
  }

  protected getRect(
    startPoint: DrawPoint,
    endPoint: DrawPoint
  ): { x: number; y: number; width: number; height: number } {
    const width = Math.abs(startPoint.x - endPoint.x);
    const height = Math.abs(startPoint.y - endPoint.y);
    const minX = Math.min(startPoint.x, endPoint.x);
    const minY = Math.min(startPoint.y, endPoint.y);
    const x = endPoint.x;
    const y = endPoint.y;
    // if (this._drawType === DrawType.flag) {
    //   return {
    //     x,
    //     y,
    //     width,
    //     height,
    //   };
    // }
    console.log(height, 'heightheight');
    return {
      x: minX,
      y: minY,
      width,
      height,
    };
  }

  setNodeId() {
    const id = new Date().getTime();
    this.node?.id(`${id}`);
  }

  /**
   * 计算2个点之间的距离
   * @param start 起始点的实际px值
   * @param end 末尾点的实际px值
   * @param ratio openseadragon的倍率
   * @returns 距离
   */
  protected pointPerimeter(
    start: PointPerimeterImagePostion,
    end: PointPerimeterImagePostion,
    ratio: OpenSeadragonRatio
  ): string {
    const a = (start.imageX * ratio.scales - end.imageX * ratio.scales) ** 2;
    const b = (start.imageY * ratio.scales - end.imageY * ratio.scales) ** 2;
    const c = (a + b) ** 0.5;
    return c.toFixed(2);
  }

  // 计算折线/曲线的长度
  protected freeLengthPerimeter(point: number[], scale: PerimeterScale, ratio: OpenSeadragonRatio) {
    let count = 0;
    for (let i = 0; i < point.length; i += 2) {
      if (i !== 0) {
        count += Number(
          this.pointPerimeter(
            { imageX: point[i - 2] * scale.scaleX, imageY: point[i - 1] * scale.scaleY },
            { imageX: point[i] * scale.scaleX, imageY: point[i + 1] * scale.scaleY },
            ratio
          )
        );
      }
    }
    return count.toFixed(2) + ratio.unit;
  }

  // 计算不规则曲线/多边形面积
  protected closedAreaPerimeter(point: number[], scale: PerimeterScale, ratio: OpenSeadragonRatio) {
    let area: any[] = [];
    for (let i = 0; i < [...point, point[0], point[1]].length - 2; i += 2) {
      area.push([
        point[i] * scale.scaleX * ratio.scales,
        point[i + 1] * scale.scaleY * ratio.scales,
      ]);
    }
    const allArea = polygonArea(area);
    // console.log(allArea, area, 'area');
    return allArea.toFixed(2) + ratio.unit + '² ';
  }

  setRatio(scale: any) {
    this._ratio = scale || { unit: 'px', scales: 1 };
  }

  // 设置组
  protected setGroup() {
    this.node?.setAttrs({
      group: this.config.group || '',
      parentGroup: this.config.parentGroup || '1',
    });
  }

  // mouseupdown or touchstart
  protected drawDown() {}

  // mousemove or touchmove
  protected drawMove() {}

  // mouseup or touchend
  protected drawUp(options: any) {}

  // contextmenu
  protected drawContextmenu() {
    this.end();
  }

  protected drawDblClick() {
    // this.end();
    this.nodeDestroy();
  }

  protected drawZoom() {
    console.log('zoom');
  }
}

//
