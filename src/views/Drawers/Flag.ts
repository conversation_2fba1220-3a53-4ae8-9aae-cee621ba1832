import { BaseDrawer } from './BaseDrawer';
import Konva from 'konva';
import OpenSeadragon, { Point } from 'openseadragon';
import { Layer } from 'konva/lib/Layer';
import { DrawType, DrawStatus } from '@/views/Marker/components/editorEnum';
import { changeFlagSizeByZoom, isMouseLeftOrTouch, FlagIsMouseLeftOrTouch } from '@/utils/index';
export class FlagDrawer extends BaseDrawer<Konva.Image> {
  private _FlagConfig: any;
  private _timer: any;
  constructor(layer: Layer, viewer: OpenSeadragon.Viewer, flagConfig: any) {
    super(layer, viewer, flagConfig);
    this._FlagConfig = flagConfig;
  }

  override drawDown() {
    this.start();
    this.startPoint = this.curPoint;
    this.node = this.createNode();
    changeFlagSizeByZoom(
      this.node as Konva.Image,
      this.viewer.viewport.getZoom(),
      this.viewer.viewport
    );
    this.layer.add(this.node);
    this.setNodeId();
  }

  override drawMove() {
    this.node?.setAttrs({
      x: this.curPoint.x,
      y: this.curPoint.y,
      pixelCoords: {
        x: this.curPoint.imageX,
        y: this.curPoint.imageY,
      },
    });
  }

  override drawUp(options: any) {
    if (FlagIsMouseLeftOrTouch(options.ev)) {
      if (this._timer) {
        clearTimeout(this._timer);
      } else {
        this.status = DrawStatus.drawingCompleted;
        if (this.node) {
          this.node.setAttrs({
            dash: [],
          });
          // this.updateHistory();
        }
        this.startPoint = {};
        this._timer = setTimeout(() => {
          this.end();
        }, 400);
      }
    }
  }

  override drawDblClick() {
    clearTimeout(this._timer);
    this.nodeDestroy();
  }

  // 创建node 抽离方便后续addmarker调用
  createNode() {
    const imageObj = new Image();
    const flagSrc =
      'data:image/png;base64,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';

    imageObj.src = flagSrc;
    const node = new Konva.Image({
      ...this._FlagConfig.config,
      x: this.curPoint.x,
      y: this.curPoint.y,
      image: imageObj,
      width: 14,
      height: 14,
      name: DrawType.flag,
      strokeScaleEnabled: false,
      offsetX: 7,
      offsetY: 14,
      zoom: this.viewer.viewport.getZoom(),
    });
    this.layer.add(node);
    this.setNodeId();
    return node;
  }
}
