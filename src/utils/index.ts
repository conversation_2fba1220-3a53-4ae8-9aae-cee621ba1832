// 图像相关的会通用的方法
import { Arrow } from 'konva/lib/shapes/Arrow';
import { Image } from 'konva/lib/shapes/Image';

// 根据zoom设置箭头的大小
export const changeArrowPointerByZoom = (arrow: Arrow, zoom: number) => {
  // zoom越小越大
  const length = 5 / zoom;
  const width = 5 / zoom;
  arrow.setAttrs?.({
    pointerLength: length,
    pointerWidth: width,
  });
};
export const changeFlagSizeByZoom = (flag: Image, zoom: number, viewport: any) => {
  console.log(flag.attrs, zoom, 'zoom');
  const k = 1;
  flag.scale({
    x: k / zoom,
    y: k / zoom,
  });
};
// 判断是否是左键或者移动端的tap  event：konva的event
export const isMouseLeftOrTouch = (event: any) => {
  const type = event.type;
  return (
    event.evt.button === 0 ||
    type === 'touchstart' ||
    type === 'touchend' ||
    type === 'dbltap'
  );
};

export const FlagIsMouseLeftOrTouch = (event: any) => {
  if (isMobile()) {
    return event.type === 'touchend' || event.type === 'touchstart';
  } else {
    return event.evt.button === 0;
  }
};

// 判断是否是移动端
export const isMobile = () => {
  const userAgent = navigator.userAgent || navigator.vendor;
  // 检查常见的移动设备标识
  return (
    userAgent.match(
      /(Safari|phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
    ) || false
  );
  // return false
};

let lastTimestamp = 0;
let sequence = 0;
let countId = 0;
export const generateId = (timestamp: number): number => {
  if (timestamp !== timestamp) {
    timestamp = Date.now();
  }
  const timestampSec = Math.floor(timestamp / 1000);

  if (timestampSec === lastTimestamp) {
    sequence += 1;
  } else {
    sequence = 0;
    lastTimestamp = timestampSec;
  }

  // 分配20位给时间戳（最大约1048575秒，即12天），剩余12位给序列号（最大4095）
  // 调整位数分配根据应用需求
  const id = (timestampSec << 12) | (sequence & 0xfff);
  // 确保结果在Int范围内
  if (id <= 0x7fffffff) {
    return id;
  } else {
    countId++;
    return countId;
  }
};

export const isReadingJson = (data: any) => {
  return (
    Object.prototype.toString.call(data) === '[object Object]' && data.Version && data.GroupModel
  );
};
